import sys
import json
import requests
from datetime import datetime
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QPushButton, QLabel, QLineEdit,
                             QComboBox, QTextEdit, QSpinBox, QListWidget, QMessageBox,
                             QGroupBox, QFrame, QTabWidget, QSizePolicy, QScrollArea, QCheckBox)
from PyQt5.QtCore import QThread, pyqtSignal, Qt, QSize, QTimer
from PyQt5.QtGui import QFont
import time
import elitesoftworks
VERSION = 1.5
SCRIPT_NAME = "beitakfemisr_requests"


class BookingWorker(QThread):
    log_signal = pyqtSignal(str)
    booking_success_signal = pyqtSignal(str)  # New signal for successful booking

    def __init__(self, attempts, delay, auth_data, service_slug):
        super().__init__()
        self.attempts = attempts
        self.delay = delay
        self.auth_data = auth_data
        self.service_slug = service_slug
        self.running = True
        self.attempt_counter = 0
        self.current_attempt_index = 0

    def run(self):
        """Main booking loop with rotation through attempts"""
        while self.running:
            # Get current attempt using rotation index
            attempt = self.attempts[self.current_attempt_index]

            if not self.running:
                break

            self.attempt_counter += 1
            self.log_signal.emit(f"\n--- المحاولة #{self.attempt_counter} ---")
            self.log_signal.emit(f"الوحدة: {attempt['display']} (المحاولة {self.current_attempt_index + 1}/{len(self.attempts)})")

            try:
                # Get unit details first
                unit_details = self.get_unit_details(attempt)
                if not unit_details:
                    self.log_signal.emit("❌ فشل الحصول على تفاصيل الوحدة")
                    self._move_to_next_attempt()
                    continue

                # Log unit details
                self.log_signal.emit("✅ تم الحصول على تفاصيل الوحدة:")
                self.log_signal.emit(f"   كود الوحدة: {unit_details.get('unitCode', 'غير متوفر')}")
                self.log_signal.emit(f"   المساحة: {unit_details.get('area', 'غير متوفر')} متر مربع")
                self.log_signal.emit(f"   عدد الغرف: {unit_details.get('roomCount', 'غير متوفر')}")
                self.log_signal.emit(f"   سعر المتر: {unit_details.get('pricePerMeter', 'غير متوفر')} جنيه")
                self.log_signal.emit(f"   رسوم الحجز المتبقية: ${unit_details.get('unitRemainingReservationFeesUSD', 'غير متوفر')}")
                self.log_signal.emit(f"   الرسوم الإدارية: ${unit_details.get('adminFeesUSD', 'غير متوفر')}")

                # Attempt booking
                success = self.book_unit(unit_details, attempt)
                if success:
                    success_message = f"تم الحجز بنجاح!\nالوحدة: {attempt['display']}\nإجمالي المحاولات: {self.attempt_counter}"
                    self.log_signal.emit("✅ تم الحجز بنجاح!")
                    self.booking_success_signal.emit(success_message)
                    self.running = False
                    return
                else:
                    self.log_signal.emit("❌ فشل الحجز")

            except Exception as e:
                self.log_signal.emit(f"❌ خطأ: {str(e)}")

            # Move to next attempt in rotation
            self._move_to_next_attempt()

            # Wait before next attempt
            if self.running:
                self.log_signal.emit(f"⏳ انتظار {self.delay} ثانية...")
                time.sleep(self.delay)

    def get_unit_details(self, attempt):
        """Get unit details for a specific attempt"""
        try:
            url = f"https://api.beitakfemisr.com/api/{self.service_slug}/fieldHandler"

            # Create API dependencies from attempt data
            api_dependencies = {}
            for key in ['reservationRequest', 'model', 'buildingNumber', 'floor', 'unitNumber']:
                if key in attempt:
                    api_dependencies[key] = attempt[key]

            payload = {"fieldId": "selectUnit", "data": {"apiDependencies": api_dependencies}, "activityId": "unitSelect"}
            headers = {
                'accept': 'application/json, text/plain, */*', 'accept-language': 'ar', 'content-type': 'application/json',
                'origin': 'https://beitakfemisr.com', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'x-csrf-token': self.auth_data['userToken'], 'Cookie': f"customer-session={self.auth_data['cookies'].get('customer-session', '')}"
            }

            response = requests.post(url, headers=headers, json=payload)
            if response.status_code == 200:
                unit_details = response.json()
                self.log_signal.emit(f"استجابة تفاصيل الوحدة: {response.status_code}")
                return unit_details
            else:
                self.log_signal.emit(f"❌ فشل الحصول على تفاصيل الوحدة: {response.status_code}")
                self.log_signal.emit(f"نص الاستجابة: {response.text[:200]}")
            return None

        except Exception as e:
            self.log_signal.emit(f'❌ خطأ في الحصول على التفاصيل: {e}')
            return None

    def book_unit(self, unit_details, attempt):
        """Attempt to book the unit"""
        try:
            url = f"https://api.beitakfemisr.com/api/{self.service_slug}"

            payload = {
                "activityId": "unitSelect",
                "residenceCountry": "AZE(+994)",
                "homeType": "unit",
                "selectUnit": [unit_details],
                "reservationRequest": attempt['reservationRequest'],
                "serviceSlug": self.service_slug
            }

            headers = {
                'accept': 'application/json, text/plain, */*',
                'accept-language': 'ar',
                'content-type': 'application/json',
                'device': 'CITIZEN',
                'origin': 'https://beitakfemisr.com',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'x-csrf-token': self.auth_data['userToken'],
                'Cookie': f"customer-session={self.auth_data['cookies'].get('customer-session', '')}"
            }

            response = requests.post(url, headers=headers, json=payload)
            self.log_signal.emit(f"كود الاستجابة: {response.status_code}")
            self.log_signal.emit(f"استجابة الحجز: {response.text}")

            if response.status_code == 200 or response.status_code == 201:
                return True
            return False

        except Exception as e:
            self.log_signal.emit(f"❌ خطأ في الحجز: {str(e)}")
            return False

    def _move_to_next_attempt(self):
        """Move to the next attempt in rotation"""
        self.current_attempt_index = (self.current_attempt_index + 1) % len(self.attempts)

    def stop(self):
        """Stop the booking worker"""
        self.running = False


class TestWorker(QThread):
    log_signal = pyqtSignal(str)
    test_details_signal = pyqtSignal(str, dict)  # attempt_display, unit_details

    def __init__(self, attempts, auth_data, service_slug):
        super().__init__()
        self.attempts = attempts
        self.auth_data = auth_data
        self.service_slug = service_slug
        self.running = True
        self.attempt_counter = 0
        self.current_attempt_index = 0

    def run(self):
        """Test rotation through attempts, showing details for each"""
        while self.running and self.attempts:
            try:
                # Get current attempt
                attempt = self.attempts[self.current_attempt_index]
                self.attempt_counter += 1

                # Log attempt header with detailed info
                self.log_signal.emit(f'\n{"="*60}')
                self.log_signal.emit(f'🔍 اختبار المحاولة #{self.attempt_counter}')
                self.log_signal.emit(f'الوحدة: {attempt["display"]} (المحاولة {self.current_attempt_index + 1}/{len(self.attempts)})')
                self.log_signal.emit(f'{"="*60}')

                # Log attempt configuration details
                self.log_signal.emit('📋 تفاصيل المحاولة:')
                self.log_signal.emit(f'   • طلب الحجز: {attempt.get("reservationRequest", "غير محدد")}')
                self.log_signal.emit(f'   • النموذج: {attempt.get("model", "غير محدد")}')
                self.log_signal.emit(f'   • رقم المبنى: {attempt.get("buildingNumber", "غير محدد")}')
                self.log_signal.emit(f'   • الدور: {attempt.get("floor", "غير محدد")}')
                self.log_signal.emit(f'   • رقم الوحدة: {attempt.get("unitNumber", "غير محدد")}')
                self.log_signal.emit(f'   • الاستكمال البديل: {attempt.get("alternativeCompletion", "غير محدد")}')
                self.log_signal.emit('')

                # Get unit details for this attempt
                self.log_signal.emit('🔄 جاري الحصول على تفاصيل الوحدة...')
                unit_details = self.get_unit_details(attempt)

                if unit_details:
                    self.log_signal.emit('✅ تم الحصول على تفاصيل الوحدة بنجاح!')

                    # Log quick summary
                    if isinstance(unit_details, dict):
                        # Extract key info for quick summary
                        summary_info = []
                        if 'unitCode' in unit_details:
                            summary_info.append(f"كود: {unit_details['unitCode']}")
                        if 'area' in unit_details:
                            summary_info.append(f"مساحة: {unit_details['area']} م²")
                        if 'totalPrice' in unit_details:
                            summary_info.append(f"سعر: {unit_details['totalPrice']}")
                        if 'availabilityStatus' in unit_details:
                            status_ar = {'available': 'متاحة', 'reserved': 'محجوزة', 'sold': 'مباعة'}.get(
                                unit_details['availabilityStatus'], unit_details['availabilityStatus'])
                            summary_info.append(f"حالة: {status_ar}")

                        if summary_info:
                            self.log_signal.emit(f'📋 ملخص سريع: {" | ".join(summary_info)}')

                    self.test_details_signal.emit(attempt["display"], unit_details)
                else:
                    self.log_signal.emit(f'❌ فشل في الحصول على تفاصيل المحاولة: {attempt["display"]}')
                    self.log_signal.emit('⚠️ قد تكون الوحدة غير متاحة أو هناك خطأ في البيانات')
                    self.log_signal.emit('💡 تحقق من صحة البيانات المدخلة أو حالة الخادم')

            except Exception as e:
                self.log_signal.emit(f"❌ خطأ في الاختبار: {str(e)}")
                import traceback
                self.log_signal.emit(f"تفاصيل الخطأ: {traceback.format_exc()}")

            # Move to next attempt in rotation
            self._move_to_next_attempt()

            # Log rotation info
            if self.running:
                next_attempt_display = self.attempts[self.current_attempt_index]["display"]
                self.log_signal.emit(f'🔄 الانتقال للمحاولة التالية: {next_attempt_display}')
                self.log_signal.emit('⏳ انتظار ثانيتين قبل المحاولة التالية...')
                self.log_signal.emit('')
                time.sleep(2)

    def get_unit_details(self, attempt):
        """Get unit details for a specific attempt"""
        try:
            url = f"https://api.beitakfemisr.com/api/{self.service_slug}/fieldHandler"

            # Create API dependencies from attempt data
            api_dependencies = {}
            for key in ['reservationRequest', 'model', 'buildingNumber', 'floor', 'unitNumber']:
                if key in attempt:
                    api_dependencies[key] = attempt[key]

            payload = {"fieldId": "selectUnit", "data": {"apiDependencies": api_dependencies}, "activityId": "unitSelect"}

            # Log API request details
            self.log_signal.emit('🌐 تفاصيل طلب API:')
            self.log_signal.emit(f'   • URL: {url}')
            self.log_signal.emit(f'   • Method: POST')
            self.log_signal.emit(f'   • Field ID: selectUnit')
            self.log_signal.emit(f'   • Activity ID: unitSelect')
            self.log_signal.emit('   • API Dependencies:')
            for key, value in api_dependencies.items():
                self.log_signal.emit(f'     - {key}: {value}')

            headers = {
                'accept': 'application/json, text/plain, */*', 'accept-language': 'ar', 'content-type': 'application/json',
                'origin': 'https://beitakfemisr.com', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'x-csrf-token': self.auth_data['userToken'], 'Cookie': f"customer-session={self.auth_data['cookies'].get('customer-session', '')}"
            }

            self.log_signal.emit('📤 إرسال الطلب...')
            response = requests.post(url, headers=headers, json=payload)

            # Log response details
            self.log_signal.emit(f'📥 استجابة الخادم:')
            self.log_signal.emit(f'   • كود الحالة: {response.status_code}')
            self.log_signal.emit(f'   • حجم الاستجابة: {len(response.content)} بايت')

            if response.status_code == 200:
                try:
                    unit_data = response.json()
                    if isinstance(unit_data, dict):
                        self.log_signal.emit(f'   • نوع البيانات: كائن JSON')
                        self.log_signal.emit(f'   • عدد الحقول: {len(unit_data)}')
                        # Log main keys
                        main_keys = list(unit_data.keys())[:5]  # First 5 keys
                        self.log_signal.emit(f'   • الحقول الرئيسية: {", ".join(main_keys)}')
                    elif isinstance(unit_data, list):
                        self.log_signal.emit(f'   • نوع البيانات: مصفوفة JSON')
                        self.log_signal.emit(f'   • عدد العناصر: {len(unit_data)}')
                    else:
                        self.log_signal.emit(f'   • نوع البيانات: {type(unit_data).__name__}')

                    return unit_data
                except json.JSONDecodeError as je:
                    self.log_signal.emit(f'❌ خطأ في تحليل JSON: {je}')
                    self.log_signal.emit(f'   • محتوى الاستجابة: {response.text[:200]}...')
                    return None
            else:
                self.log_signal.emit(f'❌ فشل الحصول على التفاصيل: {response.status_code}')
                self.log_signal.emit(f'   • رسالة الخطأ: {response.text[:200]}...')

                # Try to parse error response
                try:
                    error_data = response.json()
                    if 'message' in error_data:
                        self.log_signal.emit(f'   • رسالة الخادم: {error_data["message"]}')
                except:
                    pass

                return None

        except requests.exceptions.RequestException as re:
            self.log_signal.emit(f'❌ خطأ في الشبكة: {re}')
            return None
        except Exception as e:
            self.log_signal.emit(f'❌ خطأ عام في الحصول على التفاصيل: {e}')
            import traceback
            self.log_signal.emit(f'تفاصيل الخطأ: {traceback.format_exc()}')
            return None

    def _move_to_next_attempt(self):
        """Move to the next attempt in rotation"""
        self.current_attempt_index = (self.current_attempt_index + 1) % len(self.attempts)

    def stop(self):
        self.running = False



class BookingBot(QMainWindow):
    def __init__(self):
        super().__init__()
        self.auth_data = None
        self.services = []
        self.attempts = []
        self.worker = None
        self.test_worker = None
        self.show_test_popups = False  # Control popup display during testing

        self.field_widgets = {}
        self.dependency_chain = ['reservationRequest', 'model', 'buildingNumber', 'floor', 'unitNumber']

        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle('بوت حجز بيتك في مصر')
        self.setGeometry(100, 100, 1100, 850)
        self.setMinimumSize(900, 700)
        self.resize(1100, 850)

        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        main_layout = QVBoxLayout(main_widget)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(10, 10, 10, 10)

        self.tab_widget = QTabWidget()
        self.tab_widget.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        main_layout.addWidget(self.tab_widget)

        main_tab = QWidget()
        self.tab_widget.addTab(main_tab, "الواجهة الرئيسية")
        main_tab_layout = QVBoxLayout(main_tab)
        main_tab_layout.setContentsMargins(0, 0, 0, 0)

        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        main_tab_layout.addWidget(scroll_area)

        content_widget = QWidget()
        scroll_area.setWidget(content_widget)
        content_layout = QVBoxLayout(content_widget)
        content_layout.setSpacing(10)
        content_layout.setContentsMargins(15, 15, 15, 15)

        logs_tab = QWidget()
        self.tab_widget.addTab(logs_tab, "السجل")
        logs_tab_layout = QVBoxLayout(logs_tab)
        logs_tab_layout.setSpacing(8)
        logs_tab_layout.setContentsMargins(15, 15, 15, 15)
        
        login_group = QGroupBox('تسجيل الدخول')
        login_layout = QVBoxLayout(login_group)
        login_fields = QHBoxLayout()
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText('اسم المستخدم')
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText('كلمة المرور')
        self.password_input.setEchoMode(QLineEdit.Password)
        login_fields.addWidget(self.username_input)
        login_fields.addWidget(self.password_input)
        self.login_btn = QPushButton('🔐 تسجيل الدخول')
        self.login_btn.clicked.connect(self.login)
        login_layout.addLayout(login_fields)
        login_layout.addWidget(self.login_btn)
        content_layout.addWidget(login_group)
        
        self.user_info_label = QLabel('')
        self.user_info_label.setAlignment(Qt.AlignCenter)
        content_layout.addWidget(self.user_info_label)

        service_group = QGroupBox('اختيار الخدمة')
        service_layout = QVBoxLayout(service_group)
        self.service_combo = QComboBox()
        self.service_combo.currentIndexChanged.connect(self.on_service_changed)
        service_layout.addWidget(self.service_combo)
        content_layout.addWidget(service_group)
        
        fields_group = QGroupBox('تفاصيل الوحدة')
        fields_main_layout = QVBoxLayout(fields_group)

        self.no_filter_toggle = QCheckBox('عدم استخدام الفلتر (النظام القديم)')
        self.no_filter_toggle.stateChanged.connect(self.reset_all_fields)
        fields_main_layout.addWidget(self.no_filter_toggle)
        
        # Helper function to create field widgets
        def create_field_widget(label, field_id, has_dependency_check=True):
            layout = QVBoxLayout()
            layout.setSpacing(2)
            layout.addWidget(QLabel(label))

            widget_layout = QHBoxLayout()
            combo = QComboBox()
            combo.setEnabled(False)
            widget_layout.addWidget(combo)
            
            check = None
            if has_dependency_check:
                check = QCheckBox('فلتر')
                check.setChecked(True)
                check.setSizePolicy(QSizePolicy.Maximum, QSizePolicy.Preferred)
                widget_layout.addWidget(check)

            layout.addLayout(widget_layout)
            self.field_widgets[field_id] = {'combo': combo, 'check': check, 'layout': layout}
            combo.currentIndexChanged.connect(lambda: self.on_field_changed(field_id, user_initiated=True))

            # Add checkbox change handler
            if check:
                check.stateChanged.connect(lambda state, fid=field_id: self.on_checkbox_changed(fid, state))

            return layout

        row1 = QHBoxLayout()
        row1.addLayout(create_field_widget('طلب الحجز', 'reservationRequest'))
        row1.addLayout(create_field_widget('النموذج', 'model'))
        row1.addLayout(create_field_widget('المبنى', 'buildingNumber'))

        row2 = QHBoxLayout()
        row2.addLayout(create_field_widget('الدور', 'floor'))
        row2.addLayout(create_field_widget('الوحدة', 'unitNumber', has_dependency_check=False))

        self.alternative_combo = QComboBox()
        alternative_layout = QVBoxLayout()
        alternative_layout.addWidget(QLabel('الاستكمال'))
        alternative_layout.addWidget(self.alternative_combo)
        row2.addLayout(alternative_layout)
        
        fields_main_layout.addLayout(row1)
        fields_main_layout.addLayout(row2)
        
        self.add_btn = QPushButton('➕ إضافة محاولة')
        self.add_btn.clicked.connect(self.add_attempt)
        fields_main_layout.addWidget(self.add_btn)
        content_layout.addWidget(fields_group)
        
        attempts_group = QGroupBox('المحاولات')
        attempts_layout = QVBoxLayout(attempts_group)
        self.attempts_list = QListWidget()
        attempts_layout.addWidget(self.attempts_list)
        self.remove_btn = QPushButton('🗑️ حذف المحاولة المحددة')
        self.remove_btn.clicked.connect(self.remove_attempt)
        attempts_layout.addWidget(self.remove_btn)
        content_layout.addWidget(attempts_group, 1)
        
        control_group = QGroupBox('التحكم')
        control_layout = QVBoxLayout(control_group)
        delay_layout = QHBoxLayout()
        delay_layout.addWidget(QLabel('التأخير بين المحاولات (ثواني):'))
        self.delay_spin = QSpinBox()
        self.delay_spin.setMinimum(1)
        self.delay_spin.setMaximum(60)
        self.delay_spin.setValue(2)
        delay_layout.addWidget(self.delay_spin)
        # Test options
        test_options_layout = QHBoxLayout()
        self.show_popups_check = QCheckBox('عرض النوافذ المنبثقة أثناء الاختبار')
        self.show_popups_check.setChecked(False)
        self.show_popups_check.stateChanged.connect(self.on_popup_toggle)
        test_options_layout.addWidget(self.show_popups_check)
        control_layout.addLayout(test_options_layout)

        buttons_layout = QHBoxLayout()
        self.toggle_btn = QPushButton('▶️ بدء الحجز')
        self.toggle_btn.clicked.connect(self.toggle_booking)
        self.debug_btn = QPushButton('🔍 بدء الاختبار')
        self.debug_btn.clicked.connect(self.toggle_test)
        buttons_layout.addWidget(self.toggle_btn)
        buttons_layout.addWidget(self.debug_btn)
        control_layout.addLayout(delay_layout)
        control_layout.addLayout(buttons_layout)
        content_layout.addWidget(control_group)
        
        log_group = QGroupBox('السجل')
        log_layout = QVBoxLayout(log_group)
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        logs_tab_layout.addWidget(log_group, 1)
        
        self.update_dynamic_sizing()

    def on_popup_toggle(self, state):
        """Handle popup display toggle"""
        self.show_test_popups = state == 2  # Qt.Checked = 2
        status = "مفعل" if self.show_test_popups else "معطل"
        self.log(f'🔔 عرض النوافذ المنبثقة أثناء الاختبار: {status}')

    def resizeEvent(self, event):
        super().resizeEvent(event)
        self.update_dynamic_sizing()

    def update_dynamic_sizing(self):
        width = self.width()
        height = self.height()
        base_font_size = max(10, min(13, int((width + height) / 150)))
        button_height = max(28, min(36, int(height / 25)))
        button_padding_v = max(6, min(8, int(button_height / 4)))
        button_padding_h = max(12, min(16, int(button_height / 2)))
        self.apply_dynamic_styles(base_font_size, button_height, button_padding_v, button_padding_h)

    def apply_dynamic_styles(self, base_font_size, button_height, button_padding_v, button_padding_h):
        self.setStyleSheet(f"""
            QCheckBox {{ font-size: {base_font_size-1}px; }}
            QMainWindow {{ background-color: #f5f7fa; }}
            QGroupBox {{ font-weight: bold; font-size: {base_font_size + 1}px; border: 1px solid #e1e8ed; border-radius: 8px; margin-top: 8px; padding-top: 12px; background-color: white; }}
            QGroupBox::title {{ subcontrol-origin: margin; subcontrol-position: top right; padding: 4px 12px; color: #2c3e50; }}
            QLineEdit {{ padding: {max(8, int(button_padding_v * 1.1))}px; border: 1px solid #e1e8ed; border-radius: 6px; background-color: #ffffff; font-size: {base_font_size}px; min-height: {max(18, int(button_height * 0.6))}px; }}
            QLineEdit:focus {{ border: 2px solid #3498db; }}
            QComboBox {{ padding: {max(6, int(button_padding_v * 0.8))}px; border: 1px solid #e1e8ed; border-radius: 6px; background-color: #ffffff; min-height: {max(24, int(button_height * 0.8))}px; font-size: {base_font_size}px; }}
            QComboBox:hover {{ border: 2px solid #3498db; }}
            QComboBox::drop-down {{ border: none; width: 30px; }}
            QSpinBox {{ padding: {max(6, int(button_padding_v * 0.8))}px; border: 1px solid #e1e8ed; border-radius: 6px; background-color: #ffffff; min-height: {max(24, int(button_height * 0.8))}px; font-size: {base_font_size}px; }}
            QPushButton {{ padding: {button_padding_v}px {button_padding_h}px; border: none; border-radius: 5px; font-weight: 600; font-size: {base_font_size}px; min-height: {button_height}px; max-width: 200px; }}
            QPushButton:disabled {{ background-color: #bdc3c7; color: #7f8c8d; }}
            QListWidget {{ border: 1px solid #e1e8ed; border-radius: 6px; padding: 4px; background-color: #ffffff; font-size: {base_font_size}px; }}
            QListWidget::item {{ padding: {max(6, int(button_padding_v * 1.0))}px; border-bottom: 1px solid #ecf0f1; }}
            QListWidget::item:selected {{ background-color: #3498db; color: white; }}
            QTextEdit {{ border: 1px solid #e1e8ed; border-radius: 6px; padding: 8px; background-color: #2c3e50; color: #ecf0f1; font-family: 'Courier New', monospace; font-size: {max(9, base_font_size - 1)}px; }}
            QLabel {{ color: #2c3e50; font-size: {base_font_size}px; }}
            QTabWidget::pane {{ border: 1px solid #e1e8ed; border-radius: 6px; background-color: white; }}
            QTabBar::tab {{ background-color: #ecf0f1; color: #2c3e50; padding: {max(6, int(button_padding_v * 0.8))}px {max(12, int(button_padding_h * 0.8))}px; margin-right: 1px; border-top-left-radius: 5px; border-top-right-radius: 5px; font-weight: 600; font-size: {base_font_size}px; }}
            QTabBar::tab:selected {{ background-color: #3498db; color: white; }}
            QTabBar::tab:hover {{ background-color: #bdc3c7; }}
            QScrollArea {{ border: none; background-color: transparent; }}
            QScrollBar:vertical {{ background-color: #f0f0f0; width: 12px; border-radius: 6px; }}
            QScrollBar::handle:vertical {{ background-color: #c0c0c0; border-radius: 6px; min-height: 20px; }}
            QScrollBar::handle:vertical:hover {{ background-color: #a0a0a0; }}
        """)
        self.login_btn.setStyleSheet("background-color: #3498db; color: white;")
        self.add_btn.setStyleSheet("background-color: #2ecc71; color: white;")
        self.remove_btn.setStyleSheet("background-color: #e74c3c; color: white;")
        self.toggle_btn.setStyleSheet("background-color: #27ae60; color: white;")
        self.debug_btn.setStyleSheet("background-color: #f39c12; color: white;")
        self.user_info_label.setStyleSheet(f"background-color: #d5f4e6; color: #16a085; padding: {max(8, int(button_padding_v * 1.2))}px; border-radius: 6px; font-weight: 600; font-size: {base_font_size}px; border: 1px solid #1abc9c;")
        
    def login(self):
        username = self.username_input.text()
        password = self.password_input.text()
        
        if not username or not password:
            QMessageBox.warning(self, 'خطأ', 'الرجاء إدخال اسم المستخدم وكلمة المرور')
            return
        
        self.log('جاري تسجيل الدخول...')
        
        try:
            url = "https://api.beitakfemisr.com/api/customer/login"
            payload = {"username": username, "password": password}
            headers = {
                'accept': 'application/json, text/plain, */*', 'accept-language': 'ar', 'content-type': 'application/json',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'origin': 'https://beitakfemisr.com',
            }
            
            response = requests.post(url, headers=headers, json=payload)
            
            if response.status_code == 200:
                data = response.json()
                self.auth_data = {
                    'userId': data['userId'], 'userSlug': data['profile']['userSlug'],
                    'userToken': response.headers['x-csrf-token'], 'cookies': response.cookies.get_dict(),
                    'profile': data['profile']
                }
                
                full_name = data['profile'].get('fullName', 'غير متوفر')
                national_id = data['profile'].get('nationalId', 'غير متوفر')
                self.user_info_label.setText(f'مرحباً {full_name} | الرقم القومي: {national_id}')
                self.log('✅ تم تسجيل الدخول بنجاح')
                self.load_services()
            else:
                self.log(f'❌ فشل تسجيل الدخول: {response.status_code}')
                QMessageBox.warning(self, 'خطأ', 'فشل تسجيل الدخول')
                
        except Exception as e:
            self.log(f'❌ خطأ: {str(e)}')
            QMessageBox.critical(self, 'خطأ', str(e))

    def is_authenticated(self):
        if not self.auth_data: return False
        return all(key in self.auth_data for key in ['userId', 'userToken', 'cookies'])

    def load_services(self):
        if not self.is_authenticated(): return self.log('❌ لم يتم تسجيل الدخول')
        self.log('جاري تحميل الخدمات...')
        try:
            url = "https://api.beitakfemisr.com/api/Categories?filter=%7B%22queryKey%22:[%22categories%22],%22signal%22:%7B%7D,%22include%22:%7B%22relation%22:%22services%22,%22scope%22:%7B%22fields%22:[%22serviceDictionary%22,%22translations%22,%22activities%22,%22ID%22,%22personalization%22,%22categoryIds%22,%22createdAt%22,%22deactivated%22,%22id%22,%22logo%22,%22release%22,%22serviceLabel%22,%22description%22,%22serviceSlug%22,%22updatedAt%22,%22version%22,%22fromDate%22,%22toDate%22,%22hideServiceTable%22,%22hiddenOn%22,%22disabledOn%22,%22extraData%22,%22images%22]%7D%7D%7D"
            headers = {
                'accept': 'application/json, text/plain, */*', 'accept-language': 'ar', 'device': 'CITIZEN',
                'origin': 'https://beitakfemisr.com', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'x-csrf-token': self.auth_data['userToken'], 'Cookie': f"customer-session={self.auth_data['cookies'].get('customer-session', '')}"
            }
            response = requests.get(url, headers=headers)
            if response.status_code == 200:
                self.services = [service for cat in response.json() if 'services' in cat for service in cat['services']]
                self.service_combo.clear()

                # Add default placeholder
                self.service_combo.addItem("-- اختر خدمة --", None)

                if self.services:
                    for service in self.services:
                        label = service.get('translations', {}).get('ar', {}).get('serviceLabel', service.get('serviceLabel', 'N/A'))
                        self.service_combo.addItem(label, service)
                    self.log(f'✅ تم تحميل {len(self.services)} خدمة')
                else:
                    self.service_combo.addItem("لا يوجد خدمات متاحة", None)
                    self.service_combo.setEnabled(False)
            else:
                self.log(f'❌ فشل تحميل الخدمات: {response.status_code}')
        except Exception as e:
            self.log(f'❌ خطأ في تحميل الخدمات: {e}')
    
    def on_service_changed(self):
        service = self.service_combo.currentData()
        if service:
            self.log(f'🔄 تم اختيار الخدمة: {self.service_combo.currentText()}')
            self.load_alternative_completions(service['serviceSlug'])
            self.reset_all_fields()
        else:
            # Don't log error for placeholder selection
            if self.service_combo.currentText() not in ["-- اختر خدمة --", "لا يوجد خدمات متاحة"]:
                self.log('❌ لا توجد بيانات للخدمة المحددة')

    def reset_all_fields(self):
        """Resets all field combos and loads the initial one(s) based on the filter toggle state."""
        for field_id in self.dependency_chain:
            widgets = self.field_widgets.get(field_id)
            if widgets:
                widgets['combo'].blockSignals(True)
                widgets['combo'].clear()
                widgets['combo'].setEnabled(False)
                widgets['combo'].blockSignals(False)
                if widgets['check']:
                    widgets['check'].setEnabled(not self.no_filter_toggle.isChecked())

        use_no_filter = self.no_filter_toggle.isChecked()

        if use_no_filter:
            # Old logic: Load ALL data for ALL fields independently - no dependencies
            self.log('🔓 النظام القديم: تحميل جميع البيانات لجميع الحقول بدون قيود')
            for field_id in self.dependency_chain:
                widgets = self.field_widgets.get(field_id)
                if widgets:
                    widgets['combo'].setEnabled(True)
                    self.load_field_options(field_id)
        else:
            # New logic: only load fields that have their checkboxes checked
            # Start with reservationRequest if its checkbox is checked
            res_widgets = self.field_widgets['reservationRequest']
            if res_widgets['check'] and res_widgets['check'].isChecked():
                res_widgets['combo'].setEnabled(True)
                self.load_field_options('reservationRequest')
            
    def on_field_changed(self, changed_field_id, user_initiated=False):
        """Handles locking subsequent fields and triggering the next data load."""
        combo = self.field_widgets[changed_field_id]['combo']
        if not combo.count():
            return

        # Don't proceed if placeholder item is selected (value is None)
        current_data = combo.currentData()
        if current_data is None:
            return

        try:
            start_index = self.dependency_chain.index(changed_field_id)
        except ValueError:
            return

        # Lock and clear all subsequent fields (only in filter mode)
        use_no_filter = self.no_filter_toggle.isChecked()
        if not use_no_filter:
            for i in range(start_index + 1, len(self.dependency_chain)):
                field_to_clear = self.dependency_chain[i]
                widgets = self.field_widgets.get(field_to_clear)
                if widgets:
                    widgets['combo'].blockSignals(True)
                    widgets['combo'].clear()
                    widgets['combo'].setEnabled(False)
                    widgets['combo'].blockSignals(False)

        # Only load next field if user manually changed the field or in specific no-filter cases
        if use_no_filter:
            # In no-filter mode (old system), do nothing - all fields are independent
            # User can select any combination without cascading effects
            return
        elif user_initiated and not use_no_filter and start_index + 1 < len(self.dependency_chain):
            # In filter mode, only load next field if user manually changed this field AND next field's checkbox is checked
            next_field_id = self.dependency_chain[start_index + 1]
            next_widgets = self.field_widgets.get(next_field_id)

            # If next field has no checkbox (like unitNumber), always load it
            # If next field has checkbox, only load if checked
            if next_widgets:
                if next_widgets['check'] is None or next_widgets['check'].isChecked():
                    self.load_field_options(next_field_id)

    def on_checkbox_changed(self, field_id, state):
        """Handles checkbox state changes for field filtering."""
        if self.no_filter_toggle.isChecked():
            return  # Checkboxes are disabled in no-filter mode

        widgets = self.field_widgets.get(field_id)
        if not widgets:
            return

        is_checked = state == 2  # Qt.Checked = 2

        if is_checked:
            # If checkbox is checked and the field has data, try to load the next field
            if widgets['combo'].count() > 0:
                self.on_field_changed(field_id, user_initiated=True)
        else:
            # If checkbox is unchecked, clear all subsequent fields
            try:
                start_index = self.dependency_chain.index(field_id)
                for i in range(start_index + 1, len(self.dependency_chain)):
                    field_to_clear = self.dependency_chain[i]
                    clear_widgets = self.field_widgets.get(field_to_clear)
                    if clear_widgets:
                        clear_widgets['combo'].blockSignals(True)
                        clear_widgets['combo'].clear()
                        clear_widgets['combo'].setEnabled(False)
                        clear_widgets['combo'].blockSignals(False)
            except ValueError:
                pass

    def load_alternative_completions(self, service_slug):
        if not self.is_authenticated(): return
        self.log(f'جاري تحميل خيارات الاستكمال للخدمة: {service_slug}')
        try:
            url = f"https://api.beitakfemisr.com/api/dynamic_services/findOne?filter=%7B%22where%22:%7B%22serviceSlug%22:%22{service_slug}%22%7D,%22fields%22:%7B%22workflow%22:true,%22serviceLabel%22:true%7D%7D"
            headers = {
                'accept': 'application/json, text/plain, */*', 'accept-language': 'ar', 'origin': 'https://beitakfemisr.com',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'x-csrf-token': self.auth_data['userToken'], 'Cookie': f"customer-session={self.auth_data['cookies'].get('customer-session', '')}"
            }
            response = requests.get(url, headers=headers)
            if response.status_code == 200:
                data = response.json()
                alternatives = data['activities'][0]['steps'][0]['fieldsSchema']['selectUnit']['schema']['alternativeCompletion']
                translations = alternatives['uniforms']['translations']['ar']
                options = alternatives['uniforms'].get('options', [])
                self.alternative_combo.clear()

                # Add default placeholder
                self.alternative_combo.addItem("-- اختر الاستكمال --", None)

                if options:
                    for option in options:
                        self.alternative_combo.addItem(translations.get(option['label'], option['label']), option['value'])
                    self.log(f'✅ تم تحميل {len(options)} خيار للاستكمال')
                else:
                    self.alternative_combo.addItem("لا يوجد خيارات استكمال", None)
                    self.alternative_combo.setEnabled(False)
            else:
                self.log(f'❌ فشل تحميل خيارات الاستكمال: {response.status_code}')
        except Exception as e:
            self.log(f'❌ خطأ في تحميل خيارات الاستكمال: {e}')
    
    def load_field_options(self, field_id_to_load):
        service = self.service_combo.currentData()
        if not service or not self.is_authenticated(): return

        self.log(f'جاري تحميل خيارات {field_id_to_load}...')

        payload = {"fieldId": field_id_to_load, "data": {"apiDependencies": {}}, "activityId": "unitSelect"}

        # Determine which dependencies to apply based on checkboxes and toggle state
        try:
            use_no_filter = self.no_filter_toggle.isChecked()

            if use_no_filter:
                # When "disable all filters" is checked, send empty apiDependencies to get all data
                payload['data']['apiDependencies'] = {}
                self.log(f'🔓 تحميل جميع البيانات بدون فلتر لـ {field_id_to_load}')
            else:
                # Normal filtering mode - apply dependencies based on checkboxes
                field_index = self.dependency_chain.index(field_id_to_load)
                dependencies = self.dependency_chain[:field_index]

                for dep_id in dependencies:
                    widgets = self.field_widgets[dep_id]
                    is_filter_active = widgets['check'] and widgets['check'].isChecked()

                    if is_filter_active:
                        dep_data = widgets['combo'].currentData()
                        if dep_data:
                            payload['data']['apiDependencies'][dep_id] = dep_data

        except (ValueError, KeyError) as e:
            self.log(f"Error determining dependencies: {e}")
            return
            
        try:
            url = f"https://api.beitakfemisr.com/api/{service['serviceSlug']}/fieldHandler"
            headers = {
                'accept': 'application/json, text/plain, */*', 'accept-language': 'ar', 'content-type': 'application/json',
                'origin': 'https://beitakfemisr.com', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'x-csrf-token': self.auth_data['userToken'], 'Cookie': f"customer-session={self.auth_data['cookies'].get('customer-session', '')}"
            }
            
            response = requests.post(url, headers=headers, json=payload)

            if response.status_code == 200:
                options = response.json()
                combo = self.field_widgets[field_id_to_load]['combo']
                
                combo.blockSignals(True)
                combo.clear()

                # Always add default placeholder item first
                combo.addItem("-- اختر --", None)

                if isinstance(options, list) and len(options) > 0:
                    for option in options:
                        if isinstance(option, dict) and 'label' in option and 'value' in option:
                            combo.addItem(str(option['label']), option['value'])
                    self.log(f'✅ تم تحميل {len(options)} خيار لـ {field_id_to_load}')
                    combo.setEnabled(True)
                else:
                    # No options available - add "لا يوجد" and disable
                    combo.addItem("لا يوجد", None)
                    combo.setEnabled(False)
                    self.log(f"⚠️ لا توجد خيارات متاحة لـ {field_id_to_load}")

                combo.blockSignals(False)
                # Always start with the first item (placeholder) selected
                combo.setCurrentIndex(0)


            else:
                self.log(f'❌ فشل تحميل {field_id_to_load}: {response.status_code} - {response.text[:200]}')

        except Exception as e:
            import traceback
            self.log(f'❌ خطأ فادح في تحميل {field_id_to_load}: {e}\n{traceback.format_exc()}')
    
    def add_attempt(self):
        service = self.service_combo.currentData()
        if not service:
            return QMessageBox.warning(self, 'خطأ', 'الرجاء اختيار خدمة')
        
        required_fields = ['model', 'buildingNumber', 'floor', 'unitNumber', 'reservationRequest']
        attempt_data = {'service_slug': service['serviceSlug']}
        display_parts = []
        
        for field_id in required_fields:
            data = self.field_widgets[field_id]['combo'].currentData()
            if not data:
                return QMessageBox.warning(self, 'خطأ', f'الرجاء اختيار قيمة لحقل: {field_id}')
            attempt_data[field_id] = data
            display_parts.append(self.field_widgets[field_id]['combo'].currentText())

        alt_data = self.alternative_combo.currentData()
        if not alt_data:
            return QMessageBox.warning(self, 'خطأ', 'الرجاء اختيار قيمة للاستكمال')
        attempt_data['alternativeCompletion'] = alt_data
        
        attempt_data['display'] = " - ".join(display_parts)
        
        self.attempts.append(attempt_data)
        self.attempts_list.addItem(attempt_data['display'])
        self.log(f'✅ تمت إضافة محاولة: {attempt_data["display"]}')
    
    def remove_attempt(self):
        current_row = self.attempts_list.currentRow()
        if current_row >= 0:
            self.attempts.pop(current_row)
            self.attempts_list.takeItem(current_row)
            self.log('✅ تم حذف المحاولة')
    
    def toggle_booking(self):
        if self.worker and self.worker.isRunning():
            self.worker.stop()
            self.log('⏹️ تم إيقاف عملية الحجز')
        else:
            if not self.attempts:
                return QMessageBox.warning(self, 'خطأ', 'الرجاء إضافة محاولة واحدة على الأقل')

            service = self.service_combo.currentData()
            self.worker = BookingWorker(self.attempts, self.delay_spin.value(),
                                        self.auth_data, service['serviceSlug'])
            self.worker.log_signal.connect(self.log)
            self.worker.booking_success_signal.connect(self.on_booking_success)
            self.worker.finished.connect(self.on_booking_finished)
            self.worker.start()

            self.toggle_btn.setText('⏹️ إيقاف')
            self.toggle_btn.setStyleSheet("background-color: #e74c3c; color: white;")
            self.log(f'🚀 بدء عملية الحجز مع {len(self.attempts)} محاولة(ات) - سيتم التدوير بينها حتى النجاح...')

    def on_booking_success(self, success_message):
        """Handle successful booking completion"""
        # Stop the worker
        if self.worker:
            self.worker.stop()

        # Reset UI
        self.toggle_btn.setText('▶️ بدء الحجز')
        self.toggle_btn.setStyleSheet("background-color: #27ae60; color: white;")

        # Show success dialog in Arabic
        msg = QMessageBox()
        msg.setWindowTitle('نجح الحجز!')
        msg.setText('🎉 تهانينا! تم إكمال الحجز بنجاح')
        msg.setInformativeText(success_message)
        msg.setIcon(QMessageBox.Information)

        # Style the message box
        msg.setStyleSheet("""
            QMessageBox {
                background-color: #f8f9fa;
                font-family: Arial;
                font-size: 12px;
            }
            QMessageBox QLabel {
                color: #2c3e50;
                font-size: 14px;
                padding: 10px;
            }
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)

        # Set button text in Arabic
        msg.setStandardButtons(QMessageBox.Ok)
        msg.button(QMessageBox.Ok).setText('موافق')

        msg.exec_()

        self.log('🎉 تم إكمال الحجز بنجاح!')

    def on_booking_finished(self):
        self.toggle_btn.setText('▶️ بدء الحجز')
        self.toggle_btn.setStyleSheet("background-color: #27ae60; color: white;")
        self.log('انتهت عملية الحجز')

    def toggle_test(self):
        """Toggle test mode - start/stop cycling through attempts"""
        if self.test_worker and self.test_worker.isRunning():
            # Stop test
            self.test_worker.stop()
            self.debug_btn.setText('🔍 بدء الاختبار')
            self.debug_btn.setStyleSheet("")
            self.log('⏹️ تم إيقاف الاختبار')
        else:
            # Start test
            if not self.attempts:
                return QMessageBox.warning(self, 'خطأ', 'الرجاء إضافة محاولة واحدة على الأقل للاختبار')

            service = self.service_combo.currentData()
            if not service:
                return QMessageBox.warning(self, 'خطأ', 'الرجاء اختيار خدمة')

            if not self.is_authenticated():
                return QMessageBox.warning(self, 'خطأ', 'لم يتم تسجيل الدخول')

            # Start test worker
            self.test_worker = TestWorker(self.attempts, self.auth_data, service['serviceSlug'])
            self.test_worker.log_signal.connect(self.log)
            self.test_worker.test_details_signal.connect(self.on_test_details)
            self.test_worker.finished.connect(self.on_test_finished)
            self.test_worker.start()

            self.debug_btn.setText('⏹️ إيقاف الاختبار')
            self.debug_btn.setStyleSheet("background-color: #e74c3c; color: white;")
            self.log(f'🚀 بدء اختبار {len(self.attempts)} محاولة(ات) - سيتم التدوير بينها كل ثانيتين...')

    def on_test_details(self, attempt_display, unit_details):
        """Handle test details received from test worker"""

        # Log comprehensive unit details
        self.log('📊 تفاصيل الوحدة المسترجعة:')
        self.log('-' * 50)

        # Extract and display key information with Arabic labels
        key_fields = {
            'unitCode': 'كود الوحدة',
            'area': 'المساحة (متر مربع)',
            'roomCount': 'عدد الغرف',
            'pricePerMeter': 'سعر المتر (جنيه)',
            'totalPrice': 'السعر الإجمالي',
            'unitRemainingReservationFeesUSD': 'رسوم الحجز المتبقية (دولار)',
            'adminFeesUSD': 'الرسوم الإدارية (دولار)',
            'unitStatus': 'حالة الوحدة',
            'availabilityStatus': 'حالة التوفر',
            'floorNumber': 'رقم الدور',
            'buildingName': 'اسم المبنى',
            'unitType': 'نوع الوحدة',
            'deliveryDate': 'تاريخ التسليم',
            'paymentPlan': 'خطة الدفع'
        }

        # Display key fields if available
        for field, arabic_label in key_fields.items():
            if field in unit_details:
                value = unit_details[field]
                if value is not None and value != '':
                    self.log(f'   • {arabic_label}: {value}')

        # Display any additional fields not in the key list
        self.log('')
        self.log('📋 معلومات إضافية:')
        displayed_fields = set(key_fields.keys())
        for key, value in unit_details.items():
            if key not in displayed_fields and value is not None and value != '':
                self.log(f'   • {key}: {value}')

        # Log availability status
        if 'availabilityStatus' in unit_details:
            status = unit_details['availabilityStatus']
            if status == 'available':
                self.log('✅ الوحدة متاحة للحجز')
            elif status == 'reserved':
                self.log('🔒 الوحدة محجوزة')
            elif status == 'sold':
                self.log('❌ الوحدة مباعة')
            else:
                self.log(f'ℹ️ حالة الوحدة: {status}')

        # Log pricing summary if available
        if 'totalPrice' in unit_details or 'pricePerMeter' in unit_details:
            self.log('')
            self.log('💰 ملخص الأسعار:')
            if 'totalPrice' in unit_details:
                self.log(f'   • السعر الإجمالي: {unit_details["totalPrice"]} جنيه')
            if 'unitRemainingReservationFeesUSD' in unit_details:
                self.log(f'   • رسوم الحجز المطلوبة: ${unit_details["unitRemainingReservationFeesUSD"]}')
            if 'adminFeesUSD' in unit_details:
                self.log(f'   • الرسوم الإدارية: ${unit_details["adminFeesUSD"]}')

        self.log('-' * 50)

        # Also show a compact popup for quick reference (optional)
        if hasattr(self, 'show_test_popups') and self.show_test_popups:
            # Format key details for popup
            key_info = []
            for field, arabic_label in list(key_fields.items())[:6]:  # Show first 6 key fields
                if field in unit_details and unit_details[field] is not None:
                    key_info.append(f"{arabic_label}: {unit_details[field]}")

            popup_text = f"المحاولة: {attempt_display}\n\n" + "\n".join(key_info)

            msg = QMessageBox()
            msg.setWindowTitle(f'تفاصيل الوحدة - {attempt_display}')
            msg.setText(popup_text)
            msg.setDetailedText(f"JSON:\n{json.dumps(unit_details, ensure_ascii=False, indent=2)}")
            msg.setStandardButtons(QMessageBox.Ok)
            msg.setModal(False)  # Non-blocking
            msg.show()

            # Auto-close after 2 seconds
            QTimer.singleShot(2000, msg.close)

    def on_test_finished(self):
        """Handle test worker finished"""
        self.debug_btn.setText('🔍 بدء الاختبار')
        self.debug_btn.setStyleSheet("")
        self.log('انتهى الاختبار')

    def log(self, message):
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_message = f'[{timestamp}] {message}'
        self.log_text.append(log_message)
        with open('booking_log.txt', 'a', encoding='utf-8') as f:
            f.write(log_message + '\n')


if __name__ == '__main__':
    elitesoftworks.print_logo('1.5', 'beitakfemisr requests')
    app = QApplication(sys.argv)
    
    font = QFont()
    font.setFamily('Arial')
    font.setPointSize(10)
    app.setFont(font)
    
    window = BookingBot()
    window.show()
    sys.exit(app.exec_())